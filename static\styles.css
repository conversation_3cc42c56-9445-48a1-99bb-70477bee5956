body {
  font-family: 'Inter', sans-serif;
  background-image: url("ChatGPT Image Aug 17, 2025, 06_00_02 PM.png");
  background-size: cover;
  margin: 0;
  display: flex;
  flex-direction: row;

}

.side-bar-container {
  display: flex;
  height: 100vh;
  width: 100px;
  align-items: center;
  justify-content: center;
}

.side-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 25px;
  width: 10px;
  height: 50vh;
  border-radius: 50px;
  background-color: #121113;

}

.tool-category {
  color: #fff;
  display: flex;
  padding: 7px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-category:hover {
  background-color: #EEE5E9;
  color: #000;
}

.main-content {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
}

.tools-cards-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 20px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.middle-section {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 20px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  margin-left: 20px;
  margin-right: 20px;
}

.sessions-section {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  padding: 20px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  margin-left: 20px;
  margin-right: 20px;
  gap: 20px;
}

.finished-sessions,.running-sessions {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 20px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}
